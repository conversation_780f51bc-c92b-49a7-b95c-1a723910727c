package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"enter/internal/domain"
	"enter/internal/repository"
	"enter/internal/web/jwt"
	"enter/pkg"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UserSvc 用户服务接口
type UserSvc interface {
	// 用户查询
	GetUser(ctx context.Context, userID int64) (domain.User, error)
	GetUserByAccount(ctx context.Context, account string) (domain.User, error)
	GetUserByMobile(ctx context.Context, mobile string) (domain.User, error)

	// 用户列表
	ListUsers(ctx context.Context, req domain.UserQuery) (domain.UserList, error)
	ListUsersByCity(ctx context.Context, city string) ([]domain.User, error)
	ListUsersByRole(ctx context.Context, roleID int64) ([]domain.User, error)
	ListUsersByBDM(ctx context.Context, bdmID int64) ([]domain.User, error)

	// 用户管理
	CreateUser(ctx context.Context, req domain.UserCreation) (int64, error)
	UpdateUser(ctx context.Context, req domain.UserUpdate) error
	UpdateUserProfile(ctx context.Context, userID int64, req domain.UserProfile) error
	DisableUser(ctx context.Context, userID int64) error
	EnableUser(ctx context.Context, userID int64) error

	// 登录相关
	Login(ctx *gin.Context, req domain.UserCredentials) (domain.UserAuth, error)
	Logout(ctx *gin.Context) error
	ChangePassword(ctx context.Context, userID int64, req domain.PasswordChange) error
	ResetPassword(ctx context.Context, userID int64, newPassword string) error
}

// userSvc 用户服务实现
type userSvc struct {
	userRepo repository.UserRepo
	jwtAuth  jwt.JWT
}

// NewUserService 创建用户服务
func NewUserService(userRepo repository.UserRepo, jwtAuth jwt.JWT) UserSvc {
	return &userSvc{
		userRepo: userRepo,
		jwtAuth:  jwtAuth,
	}
}

// GetUser 获取用户信息
func (s *userSvc) GetUser(ctx context.Context, userID int64) (domain.User, error) {
	if userID <= 0 {
		return domain.User{}, errors.New("用户ID无效")
	}
	return s.userRepo.GetByID(ctx, userID)
}

// GetUserByAccount 根据账号获取用户
func (s *userSvc) GetUserByAccount(ctx context.Context, account string) (domain.User, error) {
	if account == "" {
		return domain.User{}, errors.New("账号不能为空")
	}
	return s.userRepo.GetByAccount(ctx, account)
}

// GetUserByMobile 根据手机号获取用户
func (s *userSvc) GetUserByMobile(ctx context.Context, mobile string) (domain.User, error) {
	if mobile == "" {
		return domain.User{}, errors.New("手机号不能为空")
	}
	return s.userRepo.GetByMobile(ctx, mobile)
}

// ListUsers 获取用户列表
func (s *userSvc) ListUsers(ctx context.Context, req domain.UserQuery) (domain.UserList, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	offset := (req.Page - 1) * req.PageSize

	// 根据条件查询
	var users []domain.User
	var total int64
	var err error

	if req.City != "" {
		users, err = s.userRepo.ListByCity(ctx, req.City)
		total = int64(len(users))
	} else if req.RoleID > 0 {
		users, err = s.userRepo.ListByRole(ctx, req.RoleID)
		total = int64(len(users))
	} else if req.BdmID > 0 {
		users, err = s.userRepo.ListByBDM(ctx, req.BdmID)
		total = int64(len(users))
	} else {
		users, err = s.userRepo.List(ctx, offset, req.PageSize)
		if err == nil {
			total, err = s.userRepo.Count(ctx)
		}
	}

	if err != nil {
		return domain.UserList{}, fmt.Errorf("查询用户列表失败: %w", err)
	}

	return domain.UserList{
		Users: users,
		Total: total,
		Page:  req.Page,
		Size:  len(users),
	}, nil
}

// ListUsersByCity 根据城市获取用户列表
func (s *userSvc) ListUsersByCity(ctx context.Context, city string) ([]domain.User, error) {
	if city == "" {
		return nil, errors.New("城市不能为空")
	}
	return s.userRepo.ListByCity(ctx, city)
}

// ListUsersByRole 根据角色获取用户列表
func (s *userSvc) ListUsersByRole(ctx context.Context, roleID int64) ([]domain.User, error) {
	if roleID <= 0 {
		return nil, errors.New("角色ID无效")
	}
	return s.userRepo.ListByRole(ctx, roleID)
}

// ListUsersByBDM 根据BDM获取用户列表
func (s *userSvc) ListUsersByBDM(ctx context.Context, bdmID int64) ([]domain.User, error) {
	if bdmID <= 0 {
		return nil, errors.New("BDM ID无效")
	}
	return s.userRepo.ListByBDM(ctx, bdmID)
}

// CreateUser 创建用户
func (s *userSvc) CreateUser(ctx context.Context, req domain.UserCreation) (int64, error) {
	// 检查账号是否已存在
	_, err := s.userRepo.GetByAccount(ctx, req.Account)
	if err == nil {
		return 0, errors.New("账号已存在")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, fmt.Errorf("检查账号失败: %w", err)
	}

	// 检查手机号是否已存在
	_, err = s.userRepo.GetByMobile(ctx, req.Mobile)
	if err == nil {
		return 0, errors.New("手机号已存在")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, fmt.Errorf("检查手机号失败: %w", err)
	}

	// 加密密码
	hashedPassword := pkg.MD5String(req.Password)

	// 创建用户对象
	now := time.Now().Unix()
	user := domain.User{
		Account:      req.Account,
		Password:     hashedPassword,
		RealName:     req.RealName,
		Nickname:     req.Nickname,
		Mobile:       req.Mobile,
		Sex:          req.Sex,
		City:         req.City,
		RoleID:       req.RoleID,
		EmployeeID:   req.EmployeeID,
		BdmID:        req.BdmID,
		BusinessType: req.BusinessType,
		ParentID:     req.ParentID,
		Level:        req.Level,
		IsDisable:    0, // 默认启用
		CreateTime:   now,
		UpdateTime:   now,
	}

	return s.userRepo.Create(ctx, user)
}

// UpdateUser 更新用户信息
func (s *userSvc) UpdateUser(ctx context.Context, req domain.UserUpdate) error {
	// 检查用户是否存在
	existingUser, err := s.userRepo.GetByID(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 如果更新手机号，检查是否与其他用户冲突
	if req.Mobile != "" && req.Mobile != existingUser.Mobile {
		_, err = s.userRepo.GetByMobile(ctx, req.Mobile)
		if err == nil {
			return errors.New("手机号已被其他用户使用")
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("检查手机号失败: %w", err)
		}
	}

	// 更新字段
	updateFields := []string{"update_time"}
	user := existingUser
	user.UpdateTime = time.Now().Unix()

	if req.RealName != "" {
		user.RealName = req.RealName
		updateFields = append(updateFields, "real_name")
	}
	if req.Nickname != "" {
		user.Nickname = req.Nickname
		updateFields = append(updateFields, "nickname")
	}
	if req.Mobile != "" {
		user.Mobile = req.Mobile
		updateFields = append(updateFields, "mobile")
	}
	if req.Sex >= 0 {
		user.Sex = req.Sex
		updateFields = append(updateFields, "sex")
	}
	if req.City != "" {
		user.City = req.City
		updateFields = append(updateFields, "city")
	}
	if req.RoleID > 0 {
		user.RoleID = req.RoleID
		updateFields = append(updateFields, "role_id")
	}
	if req.EmployeeID >= 0 {
		user.EmployeeID = req.EmployeeID
		updateFields = append(updateFields, "employee_id")
	}
	if req.BdmID >= 0 {
		user.BdmID = req.BdmID
		updateFields = append(updateFields, "bdm_id")
	}
	if req.BusinessType > 0 {
		user.BusinessType = req.BusinessType
		updateFields = append(updateFields, "business_type")
	}
	if req.ParentID >= 0 {
		user.ParentID = req.ParentID
		updateFields = append(updateFields, "parent_id")
	}
	if req.Level >= 0 {
		user.Level = req.Level
		updateFields = append(updateFields, "level")
	}

	return s.userRepo.Update(ctx, user, updateFields)
}

// UpdateUserProfile 更新用户个人资料
func (s *userSvc) UpdateUserProfile(ctx context.Context, userID int64, req domain.UserProfile) error {
	// 检查用户是否存在
	existingUser, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 如果更新手机号，检查是否与其他用户冲突
	if req.Mobile != "" && req.Mobile != existingUser.Mobile {
		_, err = s.userRepo.GetByMobile(ctx, req.Mobile)
		if err == nil {
			return errors.New("手机号已被其他用户使用")
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("检查手机号失败: %w", err)
		}
	}

	// 更新字段
	updateFields := []string{"update_time"}
	user := existingUser
	user.UpdateTime = time.Now().Unix()

	if req.RealName != "" {
		user.RealName = req.RealName
		updateFields = append(updateFields, "real_name")
	}
	if req.Nickname != "" {
		user.Nickname = req.Nickname
		updateFields = append(updateFields, "nickname")
	}
	if req.Mobile != "" {
		user.Mobile = req.Mobile
		updateFields = append(updateFields, "mobile")
	}
	if req.Sex >= 0 {
		user.Sex = req.Sex
		updateFields = append(updateFields, "sex")
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
		updateFields = append(updateFields, "avatar")
	}

	return s.userRepo.Update(ctx, user, updateFields)
}

// DisableUser 禁用用户
func (s *userSvc) DisableUser(ctx context.Context, userID int64) error {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	user.IsDisable = 1
	user.UpdateTime = time.Now().Unix()
	return s.userRepo.Update(ctx, user, []string{"is_disable", "update_time"})
}

// EnableUser 启用用户
func (s *userSvc) EnableUser(ctx context.Context, userID int64) error {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	user.IsDisable = 0
	user.UpdateTime = time.Now().Unix()
	return s.userRepo.Update(ctx, user, []string{"is_disable", "update_time"})
}

// Login 用户登录
func (s *userSvc) Login(ctx *gin.Context, req domain.UserCredentials) (domain.UserAuth, error) {
	// 查询用户
	user, err := s.userRepo.GetByAccount(ctx, req.Account)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.UserAuth{}, errors.New("账号或密码错误")
		}
		return domain.UserAuth{}, fmt.Errorf("查询用户失败: %w", err)
	}

	// 检查用户是否被禁用
	if user.IsDisable == 1 {
		return domain.UserAuth{}, errors.New("账号已被禁用")
	}

	// 验证密码
	hashedPassword := pkg.MD5String(req.Password)
	if user.Password != hashedPassword {
		return domain.UserAuth{}, errors.New("账号或密码错误")
	}

	// 更新登录信息
	user.LoginTime = time.Now().Unix()
	user.LoginIP = ctx.ClientIP()
	user.UpdateTime = time.Now().Unix()
	err = s.userRepo.Update(ctx, user, []string{"login_time", "login_ip", "update_time"})
	if err != nil {
		// 登录信息更新失败不影响登录流程，只记录错误
		fmt.Printf("更新用户登录信息失败: %v\n", err)
	}

	// 生成JWT token
	token, err := s.jwtAuth.GetAccessToken(ctx, jwt.Info{
		Uid:  user.ID,
		UA:   ctx.GetHeader("User-Agent"),
		Flag: "user",
	})
	if err != nil {
		return domain.UserAuth{}, fmt.Errorf("生成token失败: %w", err)
	}

	// 清除密码字段
	user.Password = ""

	return domain.UserAuth{
		User:  user,
		Token: token,
	}, nil
}

// Logout 用户登出
func (s *userSvc) Logout(ctx *gin.Context) error {
	return s.jwtAuth.ClearToken(ctx)
}

// ChangePassword 修改密码
func (s *userSvc) ChangePassword(ctx context.Context, userID int64, req domain.PasswordChange) error {
	// 查询用户
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 验证旧密码
	oldHashedPassword := pkg.MD5String(req.OldPassword)
	if user.Password != oldHashedPassword {
		return errors.New("原密码错误")
	}

	// 加密新密码
	newHashedPassword := pkg.MD5String(req.NewPassword)
	user.Password = newHashedPassword
	user.UpdateTime = time.Now().Unix()

	return s.userRepo.Update(ctx, user, []string{"password", "update_time"})
}

// ResetPassword 重置密码
func (s *userSvc) ResetPassword(ctx context.Context, userID int64, newPassword string) error {
	// 查询用户
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 加密新密码
	hashedPassword := pkg.MD5String(newPassword)
	user.Password = hashedPassword
	user.UpdateTime = time.Now().Unix()

	return s.userRepo.Update(ctx, user, []string{"password", "update_time"})
}
