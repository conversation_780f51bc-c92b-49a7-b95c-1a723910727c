# 用户服务 (UserSvc) 使用指南

用户服务提供了完整的用户管理功能，包括用户查询、创建、更新、登录、密码管理等操作。

## 功能特性

### 用户查询
- 根据用户ID查询用户信息
- 根据账号查询用户
- 根据手机号查询用户
- 获取用户列表（支持分页和条件筛选）
- 根据城市、角色、BDM筛选用户

### 用户管理
- 创建新用户
- 更新用户信息
- 更新用户个人资料
- 启用/禁用用户

### 登录认证
- 用户登录（支持JWT token生成）
- 用户登出
- 修改密码
- 重置密码

## 接口定义

### 用户查询接口

```go
// 获取用户信息
GetUser(ctx context.Context, userID int64) (domain.User, error)

// 根据账号获取用户
GetUserByAccount(ctx context.Context, account string) (domain.User, error)

// 根据手机号获取用户
GetUserByMobile(ctx context.Context, mobile string) (domain.User, error)

// 获取用户列表
ListUsers(ctx context.Context, req domain.UserQuery) (domain.UserList, error)

// 根据城市获取用户列表
ListUsersByCity(ctx context.Context, city string) ([]domain.User, error)

// 根据角色获取用户列表
ListUsersByRole(ctx context.Context, roleID int64) ([]domain.User, error)

// 根据BDM获取用户列表
ListUsersByBDM(ctx context.Context, bdmID int64) ([]domain.User, error)
```

### 用户管理接口

```go
// 创建用户
CreateUser(ctx context.Context, req CreateUserRequest) (int64, error)

// 更新用户信息
UpdateUser(ctx context.Context, req UpdateUserRequest) error

// 更新用户个人资料
UpdateUserProfile(ctx context.Context, userID int64, req UpdateProfileRequest) error

// 禁用用户
DisableUser(ctx context.Context, userID int64) error

// 启用用户
EnableUser(ctx context.Context, userID int64) error
```

### 登录认证接口

```go
// 用户登录
Login(ctx *gin.Context, req LoginRequest) (LoginResponse, error)

// 用户登出
Logout(ctx *gin.Context) error

// 修改密码
ChangePassword(ctx context.Context, userID int64, req ChangePasswordRequest) error

// 重置密码
ResetPassword(ctx context.Context, userID int64, newPassword string) error
```

## 使用示例

### 1. 创建用户服务

```go
package main

import (
    "enter/internal/service"
    "enter/internal/repository"
    "enter/internal/web/jwt"
    "enter/ioc"
)

func main() {
    // 初始化依赖
    db := ioc.CreateGormMysql()
    jwtAuth := ioc.CreateJWT()
    
    // 创建repository
    userDao := dao.NewGORMUserDao(db)
    userRepo := repository.NewCacheUserRepo(userDao)
    
    // 创建用户服务
    userSvc := service.NewUserService(userRepo, jwtAuth)
}
```

### 2. 用户查询

```go
// 获取用户信息
user, err := userSvc.GetUser(ctx, 1)
if err != nil {
    log.Printf("获取用户失败: %v", err)
    return
}
fmt.Printf("用户信息: %+v\n", user)

// 获取用户列表
req := domain.UserQuery{
    Page:     1,
    PageSize: 20,
    City:     "北京",
}
resp, err := userSvc.ListUsers(ctx, req)
if err != nil {
    log.Printf("获取用户列表失败: %v", err)
    return
}
fmt.Printf("用户列表: %d 个用户\n", resp.Total)
```

### 3. 创建用户

```go
req := domain.UserCreation{
    Account:      "newuser",
    Password:     "password123",
    RealName:     "新用户",
    Mobile:       "***********",
    Sex:          1,
    City:         "北京",
    RoleID:       1,
    BusinessType: 1,
}

userID, err := userSvc.CreateUser(ctx, req)
if err != nil {
    log.Printf("创建用户失败: %v", err)
    return
}
fmt.Printf("用户创建成功，ID: %d\n", userID)
```

### 4. 用户登录

```go
req := domain.UserCredentials{
    Account:  "testuser",
    Password: "password123",
}

resp, err := userSvc.Login(ginCtx, req)
if err != nil {
    log.Printf("登录失败: %v", err)
    return
}

fmt.Printf("登录成功，Token: %s\n", resp.Token)
fmt.Printf("用户信息: %+v\n", resp.User)
```

### 5. 更新用户信息

```go
req := domain.UserUpdate{
    ID:       1,
    RealName: "更新后的姓名",
    Mobile:   "***********",
    City:     "上海",
}

err := userSvc.UpdateUser(ctx, req)
if err != nil {
    log.Printf("更新用户失败: %v", err)
    return
}
fmt.Println("用户信息更新成功")
```

### 6. 修改密码

```go
req := domain.PasswordChange{
    OldPassword: "oldpassword",
    NewPassword: "newpassword123",
}

err := userSvc.ChangePassword(ctx, userID, req)
if err != nil {
    log.Printf("修改密码失败: %v", err)
    return
}
fmt.Println("密码修改成功")
```

## 领域结构体

**注意**: 所有业务相关的结构体现在都定义在 `internal/domain` 包中，使用更符合业务语义的命名，service 层直接使用 domain 层的结构体。

### domain.UserCreation (用户创建信息)
```go
type UserCreation struct {
    Account      string `json:"account" validate:"required,min:3,max:32"`
    Password     string `json:"password" validate:"required,min:6,max:32"`
    RealName     string `json:"real_name" validate:"required,max:32"`
    Nickname     string `json:"nickname" validate:"max:32"`
    Mobile       string `json:"mobile" validate:"required,mobile"`
    Sex          int8   `json:"sex" validate:"in:0,1,2"`
    City         string `json:"city" validate:"max:200"`
    RoleID       int64  `json:"role_id" validate:"required,min:1"`
    EmployeeID   int64  `json:"employee_id"`
    BdmID        int64  `json:"bdm_id"`
    BusinessType int8   `json:"business_type" validate:"in:1,2"`
    ParentID     int64  `json:"parent_id"`
    Level        int64  `json:"level"`
}
```

### domain.UserQuery (用户查询条件)
```go
type UserQuery struct {
    Page     int    `json:"page" form:"page"`
    PageSize int    `json:"page_size" form:"page_size"`
    City     string `json:"city" form:"city"`
    RoleID   int64  `json:"role_id" form:"role_id"`
    BdmID    int64  `json:"bdm_id" form:"bdm_id"`
    Keyword  string `json:"keyword" form:"keyword"`
}
```

### domain.UserCredentials (用户登录凭证)
```go
type UserCredentials struct {
    Account  string `json:"account" validate:"required"`
    Password string `json:"password" validate:"required"`
}
```

### domain.UserAuth (用户认证信息)
```go
type UserAuth struct {
    User  User   `json:"user"`
    Token string `json:"token"`
}
```

## 错误处理

服务中定义了常见的错误情况：

- 用户ID无效
- 账号已存在
- 手机号已存在
- 用户不存在
- 账号或密码错误
- 账号已被禁用
- 原密码错误

## 安全特性

1. **密码加密**: 使用MD5对密码进行加密存储
2. **JWT认证**: 登录成功后生成JWT token
3. **用户状态检查**: 登录时检查用户是否被禁用
4. **字段验证**: 对输入参数进行验证
5. **重复检查**: 创建用户时检查账号和手机号是否重复

## 注意事项

1. 所有涉及用户ID的操作都会验证ID的有效性
2. 创建用户时会自动检查账号和手机号的唯一性
3. 更新用户信息时只更新非空字段
4. 登录成功后会更新用户的登录时间和IP
5. 密码相关操作都会进行加密处理
6. 分页查询有最大限制（100条/页）
