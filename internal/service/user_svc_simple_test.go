package service

import (
	"testing"

	"enter/internal/domain"
	"enter/pkg"

	"github.com/stretchr/testify/assert"
)

func TestUserSvc_PasswordHashing(t *testing.T) {
	password := "testpassword123"
	hashedPassword := pkg.MD5String(password)

	// 验证密码哈希是否一致
	assert.NotEmpty(t, hashedPassword)
	assert.NotEqual(t, password, hashedPassword)
	assert.Equal(t, pkg.MD5String(password), hashedPassword)
}

func TestUserCreation_Validation(t *testing.T) {
	// 测试创建用户请求的基本结构
	req := domain.UserCreation{
		Account:      "testuser",
		Password:     "password123",
		RealName:     "Test User",
		Mobile:       "***********",
		RoleID:       1,
		BusinessType: 1,
	}

	assert.Equal(t, "testuser", req.Account)
	assert.Equal(t, "password123", req.Password)
	assert.Equal(t, "Test User", req.RealName)
	assert.Equal(t, "***********", req.Mobile)
	assert.Equal(t, int64(1), req.RoleID)
	assert.Equal(t, int8(1), req.BusinessType)
}

func TestUserCredentials_Validation(t *testing.T) {
	// 测试登录凭证的基本结构
	req := domain.UserCredentials{
		Account:  "testuser",
		Password: "password123",
	}

	assert.Equal(t, "testuser", req.Account)
	assert.Equal(t, "password123", req.Password)
}

func TestPasswordChange_Validation(t *testing.T) {
	// 测试修改密码请求的基本结构
	req := domain.PasswordChange{
		OldPassword: "oldpassword",
		NewPassword: "newpassword",
	}

	assert.Equal(t, "oldpassword", req.OldPassword)
	assert.Equal(t, "newpassword", req.NewPassword)
}

func TestUserQuery_DefaultValues(t *testing.T) {
	// 测试用户查询条件的默认值处理
	req := domain.UserQuery{}

	// 模拟服务中的默认值设置逻辑
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	assert.Equal(t, 1, req.Page)
	assert.Equal(t, 20, req.PageSize)
}

func TestUserQuery_LimitPageSize(t *testing.T) {
	// 测试页面大小限制
	req := domain.UserQuery{
		Page:     1,
		PageSize: 150, // 超过限制
	}

	// 模拟服务中的限制逻辑
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	assert.Equal(t, 100, req.PageSize)
}

func TestUserUpdate_FieldUpdates(t *testing.T) {
	// 测试更新用户请求
	req := domain.UserUpdate{
		ID:       1,
		RealName: "Updated Name",
		Mobile:   "13900139000",
		Sex:      1,
		City:     "Beijing",
		RoleID:   2,
	}

	assert.Equal(t, int64(1), req.ID)
	assert.Equal(t, "Updated Name", req.RealName)
	assert.Equal(t, "13900139000", req.Mobile)
	assert.Equal(t, int8(1), req.Sex)
	assert.Equal(t, "Beijing", req.City)
	assert.Equal(t, int64(2), req.RoleID)
}

func TestUserProfile_ProfileFields(t *testing.T) {
	// 测试更新个人资料请求
	req := domain.UserProfile{
		RealName: "New Real Name",
		Nickname: "New Nickname",
		Mobile:   "13700137000",
		Sex:      2,
		Avatar:   "http://example.com/avatar.jpg",
	}

	assert.Equal(t, "New Real Name", req.RealName)
	assert.Equal(t, "New Nickname", req.Nickname)
	assert.Equal(t, "13700137000", req.Mobile)
	assert.Equal(t, int8(2), req.Sex)
	assert.Equal(t, "http://example.com/avatar.jpg", req.Avatar)
}

func TestUserList_Structure(t *testing.T) {
	// 测试用户列表响应结构
	resp := domain.UserList{
		Users: []domain.User{},
		Total: 100,
		Page:  1,
		Size:  20,
	}

	assert.NotNil(t, resp.Users)
	assert.Equal(t, int64(100), resp.Total)
	assert.Equal(t, 1, resp.Page)
	assert.Equal(t, 20, resp.Size)
}

// 测试用户ID验证逻辑
func TestUserIDValidation(t *testing.T) {
	tests := []struct {
		name   string
		userID int64
		valid  bool
	}{
		{"Valid ID", 1, true},
		{"Valid ID 2", 100, true},
		{"Zero ID", 0, false},
		{"Negative ID", -1, false},
		{"Negative ID 2", -100, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			valid := tt.userID > 0
			assert.Equal(t, tt.valid, valid)
		})
	}
}

// 测试分页计算逻辑
func TestPaginationCalculation(t *testing.T) {
	tests := []struct {
		name     string
		page     int
		pageSize int
		offset   int
	}{
		{"First page", 1, 20, 0},
		{"Second page", 2, 20, 20},
		{"Third page", 3, 10, 20},
		{"Large page", 10, 50, 450},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			offset := (tt.page - 1) * tt.pageSize
			assert.Equal(t, tt.offset, offset)
		})
	}
}

// 测试字段更新逻辑
func TestFieldUpdateLogic(t *testing.T) {
	updateFields := []string{"update_time"}

	// 模拟字段更新逻辑
	realName := "New Name"
	if realName != "" {
		updateFields = append(updateFields, "real_name")
	}

	mobile := "***********"
	if mobile != "" {
		updateFields = append(updateFields, "mobile")
	}

	expectedFields := []string{"update_time", "real_name", "mobile"}
	assert.Equal(t, expectedFields, updateFields)
}
