package domain

import "context"

// 这个文件展示了重构后如何使用 domain 层的用户请求和响应结构体

// ExampleUserService 展示如何在 service 层使用 domain 结构体
type ExampleUserService interface {
	// 使用 domain.ListUsersRequest 而不是 service.ListUsersRequest
	ListUsers(ctx context.Context, req ListUsersRequest) (ListUsersResponse, error)
	
	// 使用 domain.CreateUserRequest 而不是 service.CreateUserRequest
	CreateUser(ctx context.Context, req CreateUserRequest) (int64, error)
	
	// 使用 domain.UpdateUserRequest 而不是 service.UpdateUserRequest
	UpdateUser(ctx context.Context, req UpdateUserRequest) error
	
	// 使用 domain.LoginRequest 和 domain.LoginResponse
	Login(ctx context.Context, req LoginRequest) (LoginResponse, error)
}

// ExampleUsage 展示如何使用重构后的结构体
func ExampleUsage() {
	// 创建用户请求 - 现在在 domain 层
	createReq := CreateUserRequest{
		Account:      "testuser",
		Password:     "password123",
		RealName:     "测试用户",
		Mobile:       "***********",
		RoleID:       1,
		BusinessType: 1,
	}
	
	// 用户列表请求 - 现在在 domain 层
	listReq := ListUsersRequest{
		Page:     1,
		PageSize: 20,
		City:     "北京",
	}
	
	// 登录请求 - 现在在 domain 层
	loginReq := LoginRequest{
		Account:  "testuser",
		Password: "password123",
	}
	
	// 更新用户请求 - 现在在 domain 层
	updateReq := UpdateUserRequest{
		ID:       1,
		RealName: "更新后的姓名",
		Mobile:   "***********",
	}
	
	// 修改密码请求 - 现在在 domain 层
	changePasswordReq := ChangePasswordRequest{
		OldPassword: "oldpassword",
		NewPassword: "newpassword123",
	}
	
	// 这些结构体现在都在 domain 包中，service 层直接使用它们
	// 这样符合了项目的分层架构：service 层与 domain 层交互
	_ = createReq
	_ = listReq
	_ = loginReq
	_ = updateReq
	_ = changePasswordReq
}
