package domain

// User 用户领域模型
type User struct {
	ID                  int64
	SN                  int64
	Avatar              string
	RealName            string
	Nickname            string
	Account             string
	Password            string
	Mobile              string
	Sex                 int8
	Channel             int8
	IsDisable           int8
	LoginIP             string
	LoginTime           int64
	IsNewUser           int8
	UserMoney           float64
	TotalRechargeAmount float64
	EmployeeID          int64
	System              int8
	BdmID               int64
	BusinessType        int8
	CreateTime          int64
	UpdateTime          int64
	DeleteTime          int64
	DingID              int64
	City                string
	TelID               string
	IsCallUser          int64
	RoleID              int64
	CallType            int8
	ParentID            int64
	Level               int64
	Version             string
}

// 用户服务请求和响应结构体

// ListUsersRequest 用户列表查询请求
type ListUsersRequest struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"page_size" form:"page_size"`
	City     string `json:"city" form:"city"`
	RoleID   int64  `json:"role_id" form:"role_id"`
	BdmID    int64  `json:"bdm_id" form:"bdm_id"`
	Keyword  string `json:"keyword" form:"keyword"` // 搜索关键词，可搜索账号、姓名、手机号
}

// ListUsersResponse 用户列表查询响应
type ListUsersResponse struct {
	Users []User `json:"users"`
	Total int64  `json:"total"`
	Page  int    `json:"page"`
	Size  int    `json:"size"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Account      string `json:"account" validate:"required,min:3,max:32"`
	Password     string `json:"password" validate:"required,min:6,max:32"`
	RealName     string `json:"real_name" validate:"required,max:32"`
	Nickname     string `json:"nickname" validate:"max:32"`
	Mobile       string `json:"mobile" validate:"required,mobile"`
	Sex          int8   `json:"sex" validate:"in:0,1,2"`
	City         string `json:"city" validate:"max:200"`
	RoleID       int64  `json:"role_id" validate:"required,min:1"`
	EmployeeID   int64  `json:"employee_id"`
	BdmID        int64  `json:"bdm_id"`
	BusinessType int8   `json:"business_type" validate:"in:1,2"`
	ParentID     int64  `json:"parent_id"`
	Level        int64  `json:"level"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	ID           int64  `json:"id" validate:"required,min:1"`
	RealName     string `json:"real_name" validate:"max:32"`
	Nickname     string `json:"nickname" validate:"max:32"`
	Mobile       string `json:"mobile" validate:"mobile"`
	Sex          int8   `json:"sex" validate:"in:0,1,2"`
	City         string `json:"city" validate:"max:200"`
	RoleID       int64  `json:"role_id" validate:"min:1"`
	EmployeeID   int64  `json:"employee_id"`
	BdmID        int64  `json:"bdm_id"`
	BusinessType int8   `json:"business_type" validate:"in:1,2"`
	ParentID     int64  `json:"parent_id"`
	Level        int64  `json:"level"`
}

// UpdateProfileRequest 更新用户个人资料请求
type UpdateProfileRequest struct {
	RealName string `json:"real_name" validate:"max:32"`
	Nickname string `json:"nickname" validate:"max:32"`
	Mobile   string `json:"mobile" validate:"mobile"`
	Sex      int8   `json:"sex" validate:"in:0,1,2"`
	Avatar   string `json:"avatar" validate:"max:200"`
}

// LoginRequest 用户登录请求
type LoginRequest struct {
	Account  string `json:"account" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// LoginResponse 用户登录响应
type LoginResponse struct {
	User  User   `json:"user"`
	Token string `json:"token"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min:6,max:32"`
}
